"use client";

import { useRef, useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Download, Edit } from "lucide-react";
import { FormData } from "../page";
import { generatePDF } from "../utils/pdfGenerator";

interface CertificatePreviewProps {
  formData: FormData;
  onBack: () => void;
  onEdit: () => void;
}

export function CertificatePreview({
  formData,
  onBack,
  onEdit,
}: CertificatePreviewProps) {
  const certificateRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(0.7);

  useEffect(() => {
    const calculateScale = () => {
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Certificate dimensions
      const certWidth = 794;
      const certHeight = 1123;

      // Calculate scale based on viewport
      const widthScale = (viewportWidth - 64) / certWidth; // 64px for padding
      const heightScale = (viewportHeight - 200) / certHeight; // 200px for UI elements

      // Use the smaller scale to ensure it fits
      let newScale = Math.min(widthScale, heightScale, 0.8);

      // Set minimum scale for very small screens
      if (viewportWidth < 480) {
        newScale = Math.min(newScale, 0.3);
      } else if (viewportWidth < 768) {
        newScale = Math.min(newScale, 0.5);
      }

      setScale(Math.max(newScale, 0.2)); // Minimum scale of 0.2
    };

    calculateScale();
    window.addEventListener("resize", calculateScale);

    return () => window.removeEventListener("resize", calculateScale);
  }, []);

  const handleDownloadPDF = async () => {
    if (certificateRef.current) {
      try {
        await generatePDF(certificateRef.current, formData);
      } catch (error) {
        console.error("Error generating PDF:", error);
        alert("Error generating PDF. Please try again.");
      }
    }
  };

  const getMiddleInitial = (middleName: string) => {
    return middleName ? middleName.charAt(0).toUpperCase() + "." : "";
  };

  const getOrdinalSuffix = (day: string) => {
    const dayNum = parseInt(day);
    if (dayNum >= 11 && dayNum <= 13) return "TH";
    switch (dayNum % 10) {
      case 1:
        return "ST";
      case 2:
        return "ND";
      case 3:
        return "RD";
      default:
        return "TH";
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-4 px-4">
      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex items-center gap-2 w-full sm:w-auto"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Form
        </Button>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <Button
            variant="outline"
            onClick={onEdit}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            <Edit className="w-4 h-4" />
            Edit
          </Button>
          <Button
            onClick={handleDownloadPDF}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            <Download className="w-4 h-4" />
            Download PDF
          </Button>
        </div>
      </div>

      {/* Certificate Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-center">Certificate Preview</CardTitle>
        </CardHeader>
        <CardContent className="p-2 sm:p-6">
          <div className="w-full overflow-x-auto">
            <div className="flex justify-center min-w-fit">
              <div
                className="bg-white shadow-lg border border-gray-200"
                style={{
                  width: `${794 * scale}px`,
                  height: `${1123 * scale}px`,
                  overflow: "hidden",
                }}
              >
                <div
                  ref={certificateRef}
                  id="certificate"
                  className="bg-white transform origin-top-left"
                  style={{
                    width: "794px",
                    height: "1123px",
                    padding: "40px",
                    fontFamily: "Arial, sans-serif",
                    position: "relative",
                    fontSize: "14px",
                    lineHeight: "1.4",
                    transform: `scale(${scale})`,
                    transformOrigin: "top left",
                    color: "#000000",
                  }}
                >
                  {/* Header */}
                  <div style={{ textAlign: "center", position: "relative" }}>
                    <img
                      src="/Tanauan_logo.jpg"
                      width="80"
                      style={{
                        position: "absolute",
                        left: "0px",
                        top: "0px",
                      }}
                      alt="Tanauan Logo"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = "none";
                      }}
                    />
                    <p style={{ marginTop: "0", marginBottom: "20px" }}>
                      Republic of the Philippines
                      <br />
                      Province of {formData.province}
                      <br />
                      Municipality of {formData.municipality}
                    </p>
                    <h3
                      style={{
                        margin: "60px 0",
                        fontSize: "18px",
                        fontWeight: "bold",
                      }}
                    >
                      CERTIFICATE OF GOOD MORAL CHARACTER/CLEARANCE
                    </h3>
                  </div>

                  {/* Content */}
                  <p style={{ marginTop: "30px", marginBottom: "20px" }}>
                    TO WHOM IT MAY CONCERN:
                  </p>

                  <p
                    style={{
                      textIndent: "40px",
                      marginBottom: "20px",
                      textAlign: "justify",
                    }}
                  >
                    THIS IS TO CERTIFY that{" "}
                    <strong>
                      {formData.lastName.toUpperCase()},{" "}
                      {formData.firstName.toUpperCase()}{" "}
                      {getMiddleInitial(formData.middleName)}
                    </strong>
                    , Filipino, <strong>{formData.age}</strong> years old, with
                    postal address at{" "}
                    <strong>Brgy. {formData.postalAddress}</strong>,{" "}
                    {formData.municipality}, {formData.province}, Philippines
                    has no derogatory record filed in this office and is a
                    person of good moral character in the community.
                  </p>

                  <p style={{ marginBottom: "20px" }}>
                    Issued this <strong>{formData.day}</strong>
                    {getOrdinalSuffix(formData.day)} day of{" "}
                    <strong>{formData.month}</strong>{" "}
                    <strong>{formData.year}</strong> at {formData.municipality},{" "}
                    {formData.province}, Philippines
                  </p>

                  {/* Mayor Section */}
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      marginTop: "20px",
                    }}
                  >
                    {/* Left container: image */}
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "start",
                      }}
                    >
                      {formData.image && (
                        <img
                          src={formData.image}
                          width="192"
                          height="192"
                          style={{
                            objectFit: "cover",
                            border: "1px solid #000",
                            aspectRatio: "1 / 1",
                          }}
                          alt="Applicant's Photo"
                        />
                      )}
                    </div>

                    {/* Right container: mayor's name */}
                    <div
                      style={{
                        flex: 1,
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                        textAlign: "center",
                      }}
                    >
                      <p
                        style={{
                          margin: 0,
                          fontSize: "18px",
                          fontWeight: "bold",
                        }}
                      >
                        {formData.mayor.toUpperCase()}
                      </p>
                      <i style={{ fontSize: "12px" }}>Municipal Mayor</i>
                    </div>
                  </div>

                  {/* fingerprint Section */}
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      marginTop: "20px",
                      marginBottom: "20px",
                    }}
                  >
                    <div style={{ display: "flex", gap: "20px" }}>
                      <div
                        style={{
                          width: "86px",
                          height: "86px",
                          border: "1px solid black",
                        }}
                      ></div>
                      <div
                        style={{
                          width: "86px",
                          height: "86px",
                          border: "1px solid black",
                        }}
                      ></div>
                    </div>
                    <div style={{ flex: 1, textAlign: "center" }}>
                      <i style={{ fontSize: "12px" }}>Documentary Stamp Paid</i>
                    </div>
                  </div>

                  {/* Signature Section */}
                  <div
                    style={{
                      marginTop: "20px",
                      marginBottom: "20px",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      width: "192px",
                      fontSize: "12px",
                    }}
                  >
                    <p
                      style={{
                        margin: "0",
                        alignItems: "center",
                        display: "flex",
                        flexDirection: "column",
                        fontWeight: "bold",
                      }}
                    >
                      {formData.lastName.toWellFormed()},{" "}
                      {formData.firstName.toLowerCase()}{" "}
                      {getMiddleInitial(formData.middleName)}
                    </p>
                    <i>Signature</i>
                  </div>

                  {/* Footer Table */}
                  <div
                    style={{
                      marginTop: "30px",
                      fontSize: "12px",
                      lineHeight: "1.6",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        gap: "40px", // Adjust spacing between groups
                      }}
                    >
                      {/* LEFT GROUP */}
                      <div style={{ flex: 1 }}>
                        <div>CTC No.: {formData.ctcNo}</div>
                        <div>
                          Issued on: {formData.month} {formData.day},{" "}
                          {formData.year}
                        </div>
                        <div>
                          Issued at: {formData.municipality},{" "}
                          {formData.province}
                        </div>
                      </div>

                      {/* RIGHT GROUP */}
                      <div style={{ flex: 1 }}>
                        <div>O.R. No.: {formData.orNo}</div>
                        <div>
                          Issued on: {formData.month} {formData.day},{" "}
                          {formData.year}
                        </div>
                        <div>
                          Issued at: {formData.municipality},{" "}
                          {formData.province}
                        </div>
                      </div>
                    </div>

                    {/* LAST LINE */}
                    <div style={{ marginTop: "8px" }}>
                      TIN No.: ____________________
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
