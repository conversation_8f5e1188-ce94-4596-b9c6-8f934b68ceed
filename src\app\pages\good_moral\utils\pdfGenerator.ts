import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { FormData } from '../page';

export async function generatePDF(element: HTMLElement, formData: FormData): Promise<void> {
  try {
    // A4 portrait dimensions in pixels (at 96 DPI)
    const A4_PORTRAIT_WIDTH = 794;   // 8.27 inches * 96 DPI
    const A4_PORTRAIT_HEIGHT = 1123; // 11.69 inches * 96 DPI

    // Create a clone of the element to avoid visual disruption
    const clonedElement = element.cloneNode(true) as HTMLElement;

    // Set up the clone with proper styling for capture
    clonedElement.style.transform = 'scale(1)';
    clonedElement.style.transformOrigin = 'top left';
    clonedElement.style.position = 'absolute';
    clonedElement.style.top = '-9999px'; // Move off-screen
    clonedElement.style.left = '-9999px';
    clonedElement.style.width = `${A4_PORTRAIT_WIDTH}px`;
    clonedElement.style.height = `${A4_PORTRAIT_HEIGHT}px`;
    clonedElement.style.backgroundColor = '#ffffff';

    // Add clone to document temporarily
    document.body.appendChild(clonedElement);

    // Configure html2canvas options for better quality
    const canvas = await html2canvas(clonedElement, {
      scale: 3, // Higher scale for better image quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: A4_PORTRAIT_WIDTH,
      height: A4_PORTRAIT_HEIGHT,
      scrollX: 0,
      scrollY: 0,
      imageTimeout: 15000, // Longer timeout for image loading
      logging: false,
    });

    // Remove the clone from document
    document.body.removeChild(clonedElement);

    // Create PDF in portrait A4 format
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Get A4 portrait dimensions in mm
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Convert canvas to image and add to PDF with higher quality
    const imgData = canvas.toDataURL('image/jpeg', 0.95);
    pdf.addImage(imgData, 'JPEG', 0, 0, pageWidth, pageHeight);

    // Generate filename with person's full name
    const fullName = `${formData.firstName}_${formData.middleName}_${formData.lastName}`.replace(/\s+/g, '_');
    const filename = `Good_Moral_Certificate(${fullName}).pdf`;

    // Save the PDF
    pdf.save(filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF');
  }
}

// Alternative method using File constructor for better browser compatibility
export async function generatePDFWithFileConstructor(element: HTMLElement, formData: FormData): Promise<void> {
  try {
    // A4 portrait dimensions in pixels (at 96 DPI)
    const A4_PORTRAIT_WIDTH = 794;   // 8.27 inches * 96 DPI
    const A4_PORTRAIT_HEIGHT = 1123; // 11.69 inches * 96 DPI

    // Create a clone of the element to avoid visual disruption
    const clonedElement = element.cloneNode(true) as HTMLElement;

    // Set up the clone with proper styling for capture
    clonedElement.style.transform = 'scale(1)';
    clonedElement.style.transformOrigin = 'top left';
    clonedElement.style.position = 'absolute';
    clonedElement.style.top = '-9999px'; // Move off-screen
    clonedElement.style.left = '-9999px';
    clonedElement.style.width = `${A4_PORTRAIT_WIDTH}px`;
    clonedElement.style.height = `${A4_PORTRAIT_HEIGHT}px`;
    clonedElement.style.backgroundColor = '#ffffff';

    // Add clone to document temporarily
    document.body.appendChild(clonedElement);

    const canvas = await html2canvas(clonedElement, {
      scale: 3,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: A4_PORTRAIT_WIDTH,
      height: A4_PORTRAIT_HEIGHT,
      imageTimeout: 15000,
      logging: false,
    });

    // Remove the clone from document
    document.body.removeChild(clonedElement);

    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Get A4 portrait dimensions in mm
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    const imgData = canvas.toDataURL('image/jpeg', 0.95);
    pdf.addImage(imgData, 'JPEG', 0, 0, pageWidth, pageHeight);

    // Get PDF as blob
    const pdfBlob = pdf.output('blob');
    
    // Create filename
    const fullName = `${formData.firstName}_${formData.middleName}_${formData.lastName}`.replace(/\s+/g, '_');
    const filename = `Good_Moral_Certificate(${fullName}).pdf`;

    // Create file and trigger download
    const file = new File([pdfBlob], filename, { type: 'application/pdf' });
    const url = URL.createObjectURL(file);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    // Clean up
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error generating PDF with File constructor:', error);
    throw new Error('Failed to generate PDF');
  }
}
